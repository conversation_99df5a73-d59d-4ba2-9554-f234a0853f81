# QuoteAI Admin Customer Onboarding System

## Overview

This document describes the secure, admin-only customer onboarding system built for QuoteAI. The system consists of a Flask backend with Firebase Authentication and a React TypeScript frontend.

## Architecture

### Backend Components

1. **Flask API Server** (`main.py`)
   - Main application server with onboarding endpoint
   - Security middleware and rate limiting
   - CORS configuration for frontend integration

2. **Firebase Authentication Middleware** (`lib/firebase_auth.py`)
   - JWT token validation using centralized Firebase initialization
   - Admin role verification via Firebase custom claims
   - Security event logging
   - Authentication decorators
   - Centralized Firebase initialization to prevent duplicate app errors

3. **Customer Data Validation** (`lib/customer_validation.py`)
   - Schema-based validation against `schema/firestore/customers.json`
   - Input sanitization to prevent injection attacks
   - Australian business validation (ABN, postcodes, phone numbers)
   - Comprehensive error reporting

### Frontend Components

1. **Admin Application** (`src/AdminApp.tsx`)
   - Main React application with routing
   - Authentication context provider
   - Protected route handling

2. **Authentication System**
   - `src/contexts/AuthContext.tsx` - Authentication state management
   - `src/components/Admin/AdminLogin.tsx` - Login interface
   - `src/components/Admin/ProtectedRoute.tsx` - Route protection

3. **Customer Onboarding Form** (`src/components/Admin/CustomerOnboardingForm.tsx`)
   - Comprehensive form matching Firestore schema
   - Real-time validation and error handling
   - Professional UI with accessibility features

4. **Styling** (`src/styles/admin.css`)
   - Professional design following existing patterns
   - Responsive layout for different screen sizes
   - Accessibility-compliant styling

## Security Features

### Authentication & Authorization
- Firebase JWT token validation
- Admin role verification via custom claims
- Secure token storage and management
- Automatic token refresh handling

### Input Security
- Comprehensive input validation and sanitization
- Protection against XSS and injection attacks
- Schema-based validation with detailed error messages
- Rate limiting on sensitive endpoints (10 requests per 5 minutes)

### Network Security
- CORS policies configured for specific origins
- Security headers (X-Content-Type-Options, X-Frame-Options, etc.)
- HTTPS enforcement ready for production
- Request/response logging for audit trails

### Data Protection
- Firestore security rules integration
- Audit logging for all admin actions
- Secure API key generation for new customers
- Timestamp tracking for all operations

## API Endpoints

### POST `/api/onboard`

**Purpose**: Create a new customer in the system

**Authentication**: Required (Firebase JWT with admin role)

**Rate Limiting**: 10 requests per 5 minutes per IP

**Request Body**: JSON matching the customer schema

**Response Codes**:
- `200`: Success - Customer onboarded
- `400`: Validation error - Invalid data
- `401`: Authentication error - Invalid/missing token
- `403`: Authorization error - Not admin
- `429`: Rate limit exceeded
- `500`: Server error

**Example Request**:
```json
{
  "basic_info": {
    "company_name": "Test Plumbing Services",
    "business_abn": "***********",
    "website_url": "https://testplumbing.com.au",
    "user_type": "tradie",
    "status": "active"
  },
  "contact_info": {
    "primary_email": "<EMAIL>",
    "address": {
      "street": "123 Test Street",
      "suburb": "Sydney",
      "state": "NSW",
      "postcode": "2000"
    }
  },
  "tradie_details": {
    "tradie_type": "plumber",
    "specializations": ["Bathroom renovation", "Kitchen plumbing"]
  },
  "ai_settings": {
    "max_quote_amount": 5000
  }
}
```

**Example Success Response**:
```json
{
  "success": true,
  "message": "Customer onboarded successfully",
  "client_id": "uuid-generated-id",
  "api_key": "uuid-generated-key"
}
```

## Setup Instructions

### Prerequisites
- Python 3.8+
- Node.js 16+
- Firebase project with Authentication and Firestore enabled
- Firebase Admin SDK credentials

### Backend Setup

1. **Install Python dependencies**:
   ```bash
   pip install flask flask-cors firebase-admin
   ```

2. **Configure Firebase**:
   - Place Firebase Admin SDK credentials in `lib/credentials.json`
   - Update project ID in database connection settings

3. **Create admin user**:
   ```bash
   python test_admin_user.py
   ```

4. **Start Flask server**:
   ```bash
   python main.py
   ```

### Frontend Setup

1. **Install Node.js dependencies**:
   ```bash
   cd ../quoteai_react-widget
   npm install react-router-dom @types/react-router-dom
   ```

2. **Configure Firebase**:
   - Update `src/firebase/config.ts` with your Firebase web app configuration
   - Ensure Authentication is enabled in Firebase Console

3. **Build the frontend**:
   ```bash
   npm run build
   ```

4. **Serve the admin interface**:
   - Copy `public/admin.html` to your web server
   - Ensure `dist/admin-interface.es.js` is accessible
   - Access via: `http://your-domain/admin.html`

### Firebase Configuration

1. **Enable Authentication**:
   - Go to Firebase Console > Authentication
   - Enable Email/Password provider
   - Set up authorized domains

2. **Set up Firestore**:
   - Create `customers` collection
   - Configure security rules for admin access

3. **Create admin user with custom claims**:
   ```javascript
   // In Firebase Admin SDK or Functions
   admin.auth().setCustomUserClaims(uid, { admin: true });
   ```

## Testing

### Backend Testing

1. **Test API without authentication**:
   ```bash
   python test_onboarding_api.py
   ```

2. **Test with valid admin token**:
   - Get Firebase ID token from frontend
   - Use in Authorization header: `Bearer <token>`

### Frontend Testing

1. **Login with admin credentials**
2. **Fill out customer onboarding form**
3. **Verify data appears in Firestore**
4. **Test validation errors**
5. **Test logout functionality**

## Deployment Considerations

### Production Security
- Replace CORS origins with actual domains
- Enable HTTPS and HSTS headers
- Use environment variables for sensitive configuration
- Implement proper logging and monitoring
- Set up Firebase security rules

### Scaling
- Consider Redis for rate limiting storage
- Implement proper session management
- Add database connection pooling
- Set up load balancing if needed

### Monitoring
- Log all admin actions for audit trails
- Monitor authentication failures
- Track API usage and performance
- Set up alerts for security events

## Troubleshooting

### Common Issues

1. **Authentication failures**:
   - Verify Firebase configuration
   - Check admin custom claims
   - Ensure token is not expired

2. **CORS errors**:
   - Update allowed origins in Flask CORS config
   - Verify frontend domain is authorized

3. **Validation errors**:
   - Check customer data against schema
   - Verify required fields are present
   - Ensure data types match expectations

4. **Build errors**:
   - Ensure all dependencies are installed
   - Check TypeScript configuration
   - Verify file paths are correct

### Support
For technical support or questions about the admin system, refer to the codebase documentation or contact the development team.

## Future Enhancements

- Business hours editing interface
- Bulk customer import functionality
- Customer data export features
- Advanced search and filtering
- Customer status management
- Integration with payment systems
- Multi-admin role management
