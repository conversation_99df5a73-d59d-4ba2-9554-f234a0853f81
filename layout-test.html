<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Step Form Layout Test</title>
    <link rel="stylesheet" href="/src/styles/admin.css">
    <style>
        /* Test-specific styles */
        .test-container {
            padding: 20px;
            background: #f0f0f0;
            margin: 20px;
            border-radius: 8px;
        }
        
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .test-pass {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .test-fail {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .test-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        /* Simulate long content */
        .long-content {
            height: 200vh;
            background: linear-gradient(to bottom, #fff, #f0f0f0);
            padding: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Multi-Step Form Layout Test</h1>
        <div id="test-results"></div>
        
        <h2>Layout Test Simulation</h2>
        <div class="multi-step-onboarding-container">
            <div class="onboarding-header">
                <h1>Customer Onboarding</h1>
                <button class="logout-button">Logout</button>
            </div>
            
            <div class="wizard-container">
                <div class="step-navigation">
                    <div class="step-progress-bar">
                        <div class="step-list">
                            <div class="step-item">
                                <div class="step-circle completed">
                                    <span class="step-number">1</span>
                                </div>
                                <div class="step-info">
                                    <h4 class="step-title">Basic Info</h4>
                                </div>
                            </div>
                            <div class="step-item">
                                <div class="step-circle current">
                                    <span class="step-number">2</span>
                                </div>
                                <div class="step-info">
                                    <h4 class="step-title">Contact Details</h4>
                                </div>
                            </div>
                            <div class="step-item">
                                <div class="step-circle pending">
                                    <span class="step-number">3</span>
                                </div>
                                <div class="step-info">
                                    <h4 class="step-title">Business</h4>
                                </div>
                            </div>
                            <div class="step-item">
                                <div class="step-circle pending">
                                    <span class="step-number">4</span>
                                </div>
                                <div class="step-info">
                                    <h4 class="step-title">AI Settings</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="step-content">
                    <div class="step-header">
                        <h2>Contact Information</h2>
                        <p>Provide contact details and business address</p>
                    </div>
                    
                    <div class="step-form">
                        <div class="form-section">
                            <h3>Email & Phone</h3>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label>Primary Email *</label>
                                    <input type="email" placeholder="<EMAIL>" />
                                </div>
                                <div class="form-group">
                                    <label>Phone Number</label>
                                    <input type="tel" placeholder="+**************" />
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-section">
                            <h3>Business Address</h3>
                            <div class="form-grid">
                                <div class="form-group form-group-full">
                                    <label>Street Address *</label>
                                    <input type="text" placeholder="123 Main Street" />
                                </div>
                                <div class="form-group">
                                    <label>Suburb *</label>
                                    <input type="text" placeholder="Sydney" />
                                </div>
                                <div class="form-group">
                                    <label>State *</label>
                                    <select>
                                        <option value="">Select State</option>
                                        <option value="NSW">NSW</option>
                                        <option value="VIC">VIC</option>
                                        <option value="QLD">QLD</option>
                                        <option value="WA">WA</option>
                                        <option value="SA">SA</option>
                                        <option value="TAS">TAS</option>
                                        <option value="ACT">ACT</option>
                                        <option value="NT">NT</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>Postcode *</label>
                                    <input type="text" placeholder="2000" />
                                </div>
                            </div>
                        </div>
                        
                        <!-- Add extra content to test scrolling -->
                        <div class="form-section">
                            <h3>Additional Information</h3>
                            <div class="form-grid">
                                <div class="form-group form-group-full">
                                    <label>Company Description</label>
                                    <textarea rows="4" placeholder="Describe your business..."></textarea>
                                </div>
                                <div class="form-group">
                                    <label>Industry</label>
                                    <select>
                                        <option value="">Select Industry</option>
                                        <option value="technology">Technology</option>
                                        <option value="healthcare">Healthcare</option>
                                        <option value="finance">Finance</option>
                                        <option value="retail">Retail</option>
                                        <option value="manufacturing">Manufacturing</option>
                                        <option value="education">Education</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>Company Size</label>
                                    <select>
                                        <option value="">Select Size</option>
                                        <option value="1-10">1-10 employees</option>
                                        <option value="11-50">11-50 employees</option>
                                        <option value="51-200">51-200 employees</option>
                                        <option value="201-500">201-500 employees</option>
                                        <option value="500+">500+ employees</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- More content to test scrolling -->
                        <div class="form-section">
                            <h3>Contact Preferences</h3>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label>Preferred Contact Method</label>
                                    <select>
                                        <option value="">Select Method</option>
                                        <option value="email">Email</option>
                                        <option value="phone">Phone</option>
                                        <option value="sms">SMS</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>Best Time to Contact</label>
                                    <select>
                                        <option value="">Select Time</option>
                                        <option value="morning">Morning (9AM-12PM)</option>
                                        <option value="afternoon">Afternoon (12PM-5PM)</option>
                                        <option value="evening">Evening (5PM-8PM)</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="step-actions">
                    <button type="button" class="btn-secondary">Previous</button>
                    <button type="button" class="btn-primary">Next Step</button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Test functions
        function runLayoutTests() {
            const results = [];
            
            // Test 1: Check if buttons are visible
            const buttons = document.querySelectorAll('.step-actions button');
            const buttonsVisible = Array.from(buttons).every(btn => {
                const rect = btn.getBoundingClientRect();
                return rect.height > 0 && rect.width > 0;
            });
            
            results.push({
                name: 'Navigation Buttons Visibility',
                passed: buttonsVisible,
                message: buttonsVisible ? 'All navigation buttons are visible' : 'Some navigation buttons are not visible'
            });
            
            // Test 2: Check if form fields are accessible
            const formFields = document.querySelectorAll('input, select, textarea');
            const fieldsAccessible = Array.from(formFields).every(field => {
                const rect = field.getBoundingClientRect();
                return rect.height > 0 && rect.width > 0;
            });
            
            results.push({
                name: 'Form Fields Accessibility',
                passed: fieldsAccessible,
                message: fieldsAccessible ? 'All form fields are accessible' : 'Some form fields are not accessible'
            });
            
            // Test 3: Check if container allows scrolling
            const container = document.querySelector('.multi-step-onboarding-container');
            const containerScrollable = container.scrollHeight > container.clientHeight || 
                                      document.body.scrollHeight > window.innerHeight;
            
            results.push({
                name: 'Scrollable Layout',
                passed: containerScrollable,
                message: containerScrollable ? 'Layout supports scrolling when content overflows' : 'Layout may not support proper scrolling'
            });
            
            // Test 4: Check if wizard container has proper margins
            const wizardContainer = document.querySelector('.wizard-container');
            const containerStyle = window.getComputedStyle(wizardContainer);
            const hasBottomMargin = parseInt(containerStyle.marginBottom) > 0;
            
            results.push({
                name: 'Container Spacing',
                passed: hasBottomMargin,
                message: hasBottomMargin ? 'Wizard container has proper bottom spacing' : 'Wizard container may lack proper spacing'
            });
            
            // Display results
            displayTestResults(results);
        }
        
        function displayTestResults(results) {
            const container = document.getElementById('test-results');
            container.innerHTML = '';
            
            results.forEach(result => {
                const div = document.createElement('div');
                div.className = `test-result ${result.passed ? 'test-pass' : 'test-fail'}`;
                div.innerHTML = `
                    <strong>${result.name}:</strong> ${result.passed ? 'PASS' : 'FAIL'}<br>
                    <small>${result.message}</small>
                `;
                container.appendChild(div);
            });
            
            // Add viewport info
            const infoDiv = document.createElement('div');
            infoDiv.className = 'test-result test-info';
            infoDiv.innerHTML = `
                <strong>Viewport Info:</strong><br>
                <small>
                    Window: ${window.innerWidth}x${window.innerHeight}<br>
                    Document: ${document.body.scrollWidth}x${document.body.scrollHeight}<br>
                    Scroll Position: ${window.scrollX}, ${window.scrollY}
                </small>
            `;
            container.appendChild(infoDiv);
        }
        
        // Run tests when page loads
        window.addEventListener('load', () => {
            setTimeout(runLayoutTests, 500);
        });
        
        // Re-run tests on resize
        window.addEventListener('resize', () => {
            setTimeout(runLayoutTests, 100);
        });
    </script>
</body>
</html>
