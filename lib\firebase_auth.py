"""
Firebase Authentication middleware for admin verification.
Provides JWT token validation and admin role checking.
"""

import functools
import logging
from typing import Optional, Dict, Any, Tuple
from flask import request, jsonify, g
from firebase_admin import auth, firestore
import firebase_admin
from db import connection

# Setup logging
logger = logging.getLogger(__name__)

def ensure_firebase_initialized():
    """
    Ensure Firebase is properly initialized.
    This function ensures the Firebase app exists before using auth functions.
    """
    try:
        # Try to get the default app
        firebase_admin.get_app()
    except ValueError:
        # If app doesn't exist, initialize it via the connection module
        connection.get_db_client_prod()

class AuthenticationError(Exception):
    """Custom exception for authentication errors"""
    pass

class AuthorizationError(Exception):
    """Custom exception for authorization errors"""
    pass

def extract_bearer_token(authorization_header: str) -> Optional[str]:
    """
    Extract Bearer token from Authorization header.
    
    Args:
        authorization_header: The Authorization header value
        
    Returns:
        The token string or None if invalid format
    """
    if not authorization_header:
        return None
    
    parts = authorization_header.split(' ')
    if len(parts) != 2 or parts[0].lower() != 'bearer':
        return None
    
    return parts[1]

def verify_firebase_token(id_token: str) -> Tuple[Dict[str, Any], str]:
    """
    Verify Firebase ID token and return decoded claims.

    Args:
        id_token: The Firebase ID token to verify

    Returns:
        Tuple of (decoded_token, uid)

    Raises:
        AuthenticationError: If token is invalid or expired
    """
    try:
        # Ensure Firebase is properly initialized
        ensure_firebase_initialized()

        # Verify the ID token
        decoded_token = auth.verify_id_token(id_token)
        uid = decoded_token['uid']

        logger.info(f"Successfully verified token for user: {uid}")
        return decoded_token, uid

    except auth.InvalidIdTokenError as e:
        logger.warning(f"Invalid ID token: {str(e)}")
        raise AuthenticationError("Invalid or expired token")
    except auth.ExpiredIdTokenError as e:
        logger.warning(f"Expired ID token: {str(e)}")
        raise AuthenticationError("Token has expired")
    except Exception as e:
        logger.error(f"Token verification error: {str(e)}")
        raise AuthenticationError("Token verification failed")

def check_admin_role(uid: str, decoded_token: Dict[str, Any]) -> bool:
    """
    Check if user has admin role via custom claims.

    Args:
        uid: User ID
        decoded_token: Decoded Firebase token

    Returns:
        True if user is admin, False otherwise

    Raises:
        AuthorizationError: If user is not authorized
    """
    try:
        # Ensure Firebase is properly initialized
        ensure_firebase_initialized()

        # Check custom claims for admin role
        custom_claims = decoded_token.get('admin', False)

        if custom_claims is True:
            logger.info(f"Admin access granted for user: {uid}")
            return True

        # Fallback: Check user record for custom claims
        user_record = auth.get_user(uid)
        admin_claim = user_record.custom_claims.get('admin', False) if user_record.custom_claims else False

        if admin_claim is True:
            logger.info(f"Admin access granted via user record for user: {uid}")
            return True

        logger.warning(f"Admin access denied for user: {uid}")
        raise AuthorizationError("Insufficient permissions - admin role required")

    except auth.UserNotFoundError:
        logger.error(f"User not found: {uid}")
        raise AuthorizationError("User not found")
    except Exception as e:
        logger.error(f"Admin role check error: {str(e)}")
        raise AuthorizationError("Authorization check failed")

def log_security_event(event_type: str, uid: Optional[str] = None, 
                      ip_address: Optional[str] = None, details: Optional[str] = None):
    """
    Log security events for audit purposes.
    
    Args:
        event_type: Type of security event
        uid: User ID if available
        ip_address: Client IP address
        details: Additional details about the event
    """
    try:
        fb_client = connection.get_db_client_prod()
        
        security_log = {
            'event_type': event_type,
            'uid': uid,
            'ip_address': ip_address,
            'details': details,
            'timestamp': firestore.SERVER_TIMESTAMP,
            'user_agent': request.headers.get('User-Agent', 'Unknown')
        }
        
        fb_client.collection('security_logs').add(security_log)
        logger.info(f"Security event logged: {event_type}")
        
    except Exception as e:
        logger.error(f"Failed to log security event: {str(e)}")

def require_admin_auth(f):
    """
    Decorator to require Firebase admin authentication for endpoints.
    
    Usage:
        @app.route('/admin/endpoint')
        @require_admin_auth
        def admin_endpoint():
            # Access authenticated user via g.current_user
            return jsonify({'message': 'Admin access granted'})
    """
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            # Get Authorization header
            auth_header = request.headers.get('Authorization')
            if not auth_header:
                log_security_event('AUTH_MISSING_TOKEN', 
                                 ip_address=request.remote_addr,
                                 details='No Authorization header provided')
                return jsonify({'error': 'Authorization header required'}), 401
            
            # Extract Bearer token
            id_token = extract_bearer_token(auth_header)
            if not id_token:
                log_security_event('AUTH_INVALID_FORMAT', 
                                 ip_address=request.remote_addr,
                                 details='Invalid Authorization header format')
                return jsonify({'error': 'Invalid Authorization header format'}), 401
            
            # Verify Firebase token
            decoded_token, uid = verify_firebase_token(id_token)
            
            # Check admin role
            check_admin_role(uid, decoded_token)
            
            # Store user info in Flask's g object for use in the endpoint
            g.current_user = {
                'uid': uid,
                'email': decoded_token.get('email'),
                'email_verified': decoded_token.get('email_verified', False),
                'token': decoded_token
            }
            
            log_security_event('AUTH_SUCCESS', 
                             uid=uid,
                             ip_address=request.remote_addr,
                             details='Admin authentication successful')
            
            return f(*args, **kwargs)
            
        except AuthenticationError as e:
            log_security_event('AUTH_FAILED', 
                             ip_address=request.remote_addr,
                             details=str(e))
            return jsonify({'error': str(e)}), 401
            
        except AuthorizationError as e:
            log_security_event('AUTH_UNAUTHORIZED', 
                             ip_address=request.remote_addr,
                             details=str(e))
            return jsonify({'error': str(e)}), 403
            
        except Exception as e:
            logger.error(f"Unexpected authentication error: {str(e)}")
            log_security_event('AUTH_ERROR', 
                             ip_address=request.remote_addr,
                             details=f'Unexpected error: {str(e)}')
            return jsonify({'error': 'Authentication failed'}), 500
    
    return decorated_function

def create_admin_user(email: str, password: str) -> str:
    """
    Helper function to create an admin user with custom claims.
    This should only be used for initial setup or by existing admins.

    Args:
        email: Admin user email
        password: Admin user password

    Returns:
        User ID of created admin
    """
    try:
        # Ensure Firebase is properly initialized
        ensure_firebase_initialized()

        # Create user
        user = auth.create_user(
            email=email,
            password=password,
            email_verified=True
        )

        # Set admin custom claim
        auth.set_custom_user_claims(user.uid, {'admin': True})

        logger.info(f"Admin user created: {email} (UID: {user.uid})")

        log_security_event('ADMIN_USER_CREATED',
                         uid=user.uid,
                         details=f'Admin user created: {email}')

        return user.uid

    except Exception as e:
        logger.error(f"Failed to create admin user: {str(e)}")
        raise Exception(f"Failed to create admin user: {str(e)}")
