<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>QuoteAI Admin - Customer Onboarding</title>
  <link rel="icon" type="image/png" href="/quoteai.png">
  
  <!-- Meta tags for security -->
  <meta http-equiv="X-Content-Type-Options" content="nosniff">
  <meta http-equiv="X-Frame-Options" content="DENY">
  <meta http-equiv="X-XSS-Protection" content="1; mode=block">
  
  <!-- Preload critical resources -->
  <link rel="preload" href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600&display=swap" as="style">
  <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600&display=swap" rel="stylesheet">
  
  <style>
    /* Critical CSS for loading state */
    body {
      margin: 0;
      padding: 0;
      font-family: 'Open Sans', sans-serif;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      min-height: 100vh;
    }
    
    #admin-loading {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      transition: opacity 0.5s ease;
    }
    
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #e1e5e9;
      border-top: 4px solid #1b8ae4;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 20px;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .loading-text {
      color: #666;
      font-size: 16px;
      font-weight: 500;
    }
    
    .loading-subtext {
      color: #999;
      font-size: 14px;
      margin-top: 8px;
    }
    
    /* Hide loading screen when admin is loaded */
    body.admin-loaded #admin-loading {
      opacity: 0;
      pointer-events: none;
    }
    
    /* Loading container styles for protected routes */
    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    }
    
    .loading-container .loading-spinner {
      width: 32px;
      height: 32px;
      border-width: 3px;
      margin-bottom: 16px;
    }
    
    .loading-container p {
      color: #666;
      font-size: 14px;
      margin: 0;
    }
    
    /* Access denied styles */
    .access-denied-container {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      padding: 20px;
    }
    
    .access-denied-card {
      background: white;
      border-radius: 12px;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      padding: 40px;
      text-align: center;
      max-width: 400px;
    }
    
    .access-denied-card h1 {
      color: #dc3545;
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 16px 0;
    }
    
    .access-denied-card p {
      color: #666;
      font-size: 14px;
      margin: 0 0 24px 0;
      line-height: 1.4;
    }
    
    .access-denied-card button {
      background-color: #1b8ae4;
      color: white;
      border: none;
      border-radius: 8px;
      padding: 12px 24px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.2s ease;
    }
    
    .access-denied-card button:hover {
      background-color: #1570c7;
    }
  </style>
</head>
<body>
  <!-- Loading screen -->
  <div id="admin-loading">
    <div class="loading-spinner"></div>
    <div class="loading-text">Loading QuoteAI Admin</div>
    <div class="loading-subtext">Customer Onboarding System</div>
  </div>

  <!-- Admin app will be rendered here -->
  <div id="admin-root"></div>

  <!-- Load React and ReactDOM from CDN -->
  <script src="https://unpkg.com/react@18.0.0/umd/react.development.js"></script>
  <script src="https://unpkg.com/react-dom@18.0.0/umd/react-dom.development.js"></script>
  <script src="https://unpkg.com/react-router-dom@6.8.0/dist/umd/react-router-dom.development.js"></script>

  <!-- Load Firebase SDK -->
  <script src="https://www.gstatic.com/firebasejs/11.6.0/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/11.6.0/firebase-auth-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/11.6.0/firebase-firestore-compat.js"></script>

  <!-- Load admin interface -->
  <script type="module" src="/dist/admin-interface.es.js"></script>
  <link rel="stylesheet" href="/src\styles\admin.css">

  <script>
    // Hide loading screen after admin interface loads
    setTimeout(() => {
      const loadingScreen = document.getElementById('admin-loading');
      if (loadingScreen) {
        document.body.classList.add('admin-loaded');
        setTimeout(() => loadingScreen.remove(), 500);
      }
    }, 2000);

    // Error handling
    window.addEventListener('error', (event) => {
      console.error('Admin interface error:', event.error);
      const loadingText = document.querySelector('.loading-text');
      if (loadingText) {
        loadingText.textContent = 'Error loading admin interface';
        loadingText.style.color = '#dc3545';
      }
    });
  </script>
</body>
</html>
