#!/usr/bin/env python3
"""
Setup script for the QuoteAI Admin Customer Onboarding System.
This script helps with initial setup and configuration.
"""

import os
import sys
import subprocess
import json

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True

def check_node_version():
    """Check if Node.js is installed and compatible."""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ Node.js {version} detected")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ Node.js is required but not found")
    return False

def install_python_dependencies():
    """Install required Python packages."""
    print("\n📦 Installing Python dependencies...")
    
    packages = [
        'flask',
        'flask-cors',
        'firebase-admin',
        'requests'  # For testing
    ]
    
    try:
        for package in packages:
            print(f"Installing {package}...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                         check=True, capture_output=True)
        print("✅ Python dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install Python dependencies: {e}")
        return False

def install_node_dependencies():
    """Install required Node.js packages."""
    print("\n📦 Installing Node.js dependencies...")
    
    # Check if we're in the React widget directory
    react_dir = "../quoteai_react-widget"
    if not os.path.exists(react_dir):
        print(f"❌ React project directory not found: {react_dir}")
        return False
    
    try:
        # Change to React directory and install dependencies
        original_dir = os.getcwd()
        os.chdir(react_dir)
        
        print("Installing react-router-dom...")
        subprocess.run(['npm', 'install', 'react-router-dom', '@types/react-router-dom'], 
                      check=True, capture_output=True)
        
        print("✅ Node.js dependencies installed successfully")
        os.chdir(original_dir)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install Node.js dependencies: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def check_firebase_config():
    """Check if Firebase configuration exists."""
    print("\n🔥 Checking Firebase configuration...")
    
    # Check for Firebase Admin SDK credentials
    if os.path.exists("lib/credentials.json"):
        print("✅ Firebase Admin SDK credentials found")
    else:
        print("⚠️  Firebase Admin SDK credentials not found (lib/credentials.json)")
        print("   You'll need to download this from Firebase Console > Project Settings > Service Accounts")
    
    # Check for Firebase web config
    web_config_path = "../quoteai_react-widget/src/firebase/config.ts"
    if os.path.exists(web_config_path):
        print("✅ Firebase web configuration found")
        
        # Check if it contains placeholder values
        with open(web_config_path, 'r') as f:
            content = f.read()
            if "TODO:" in content or "Replace with actual" in content:
                print("⚠️  Firebase web configuration contains placeholder values")
                print("   Update src/firebase/config.ts with your actual Firebase web app config")
    else:
        print("⚠️  Firebase web configuration not found")
    
    return True

def build_frontend():
    """Build the React frontend."""
    print("\n🏗️  Building React frontend...")
    
    react_dir = "../quoteai_react-widget"
    if not os.path.exists(react_dir):
        print(f"❌ React project directory not found: {react_dir}")
        return False
    
    try:
        original_dir = os.getcwd()
        os.chdir(react_dir)
        
        print("Running npm run build...")
        result = subprocess.run(['npm', 'run', 'build'], 
                              check=True, capture_output=True, text=True)
        
        print("✅ Frontend built successfully")
        os.chdir(original_dir)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Frontend build failed: {e}")
        if e.stdout:
            print("STDOUT:", e.stdout)
        if e.stderr:
            print("STDERR:", e.stderr)
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def run_tests():
    """Run validation tests."""
    print("\n🧪 Running validation tests...")
    
    try:
        result = subprocess.run([sys.executable, 'test_validation.py'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ All validation tests passed")
            return True
        else:
            print("❌ Some validation tests failed")
            print(result.stdout)
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return False

def print_next_steps():
    """Print next steps for the user."""
    print("\n" + "="*60)
    print("🎉 SETUP COMPLETE!")
    print("="*60)
    print("\nNext steps:")
    print("\n1. 🔥 Configure Firebase:")
    print("   - Download Firebase Admin SDK credentials to lib/credentials.json")
    print("   - Update src/firebase/config.ts with your web app config")
    print("   - Enable Authentication and Firestore in Firebase Console")
    print("\n2. 👤 Create an admin user:")
    print("   python test_admin_user.py")
    print("\n3. 🚀 Start the Flask server:")
    print("   python main.py")
    print("\n4. 🌐 Access the admin interface:")
    print("   http://localhost:5173/admin.html")
    print("   (or serve the built files from your web server)")
    print("\n5. 🧪 Test the system:")
    print("   python test_onboarding_api.py")
    print("\n📚 For detailed documentation, see:")
    print("   ADMIN_SYSTEM_DOCUMENTATION.md")
    print("\n" + "="*60)

def main():
    """Main setup function."""
    print("QuoteAI Admin Customer Onboarding System Setup")
    print("=" * 50)
    
    # Check prerequisites
    if not check_python_version():
        return False
    
    if not check_node_version():
        return False
    
    # Install dependencies
    if not install_python_dependencies():
        return False
    
    if not install_node_dependencies():
        return False
    
    # Check Firebase configuration
    check_firebase_config()
    
    # Build frontend
    if not build_frontend():
        return False
    
    # Run tests
    if not run_tests():
        print("⚠️  Tests failed, but setup can continue")
    
    # Print next steps
    print_next_steps()
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
