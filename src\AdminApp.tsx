/**
 * Main admin application with routing
 */

import React from 'react';
import { HashRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import AdminLogin from './components/Admin/AdminLogin';
import MultiStepOnboardingForm from './components/Admin/MultiStepOnboardingForm';
import ProtectedRoute from './components/Admin/ProtectedRoute';
import { useAuth } from './contexts/AuthContext';
import './styles/admin.css';

const AdminRoutes: React.FC = () => {
  const { logout } = useAuth();

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    <Routes>
      <Route path="/login" element={<AdminLogin />} />
      <Route
        path="/onboard"
        element={
          <ProtectedRoute>
            <MultiStepOnboardingForm onLogout={handleLogout} />
          </ProtectedRoute>
        }
      />
      <Route path="/" element={<Navigate to="/onboard" replace />} />
      <Route path="*" element={<Navigate to="/onboard" replace />} />
    </Routes>
  );
};

const AdminApp: React.FC = () => {
  return (
    <AuthProvider>
      <Router>
        <div className="admin-app">
          <AdminRoutes />
        </div>
      </Router>
    </AuthProvider>
  );
};

export default AdminApp;
