/**
 * Customer onboarding form component
 */

import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import TagInput from './TagInput';

interface CustomerData {
  basic_info: {
    company_name: string;
    business_abn: string;
    website_url: string;
    user_type: string;
    status: string;
  };
  contact_info: {
    primary_email: string;
    secondary_emails: string[];
    phone: string;
    mobile: string;
    address: {
      street: string;
      suburb: string;
      state: string;
      postcode: string;
    };
  };
  tradie_details: {
    tradie_type: string;
    specializations: string[];
    service_areas: {
      suburbs: string[];
      postcodes: string[];
      radius_km: number;
    };
    business_hours: {
      monday: { open: string; close: string };
      tuesday: { open: string; close: string };
      wednesday: { open: string; close: string };
      thursday: { open: string; close: string };
      friday: { open: string; close: string };
      saturday: { open: string; close: string };
      sunday: { open: string; close: string };
      timezone: string;
    };
  };
  ai_settings: {
    greeting_message: string;
    custom_instructions: string;
    response_tone: string;
    max_quote_amount: number;
  };
}

const initialCustomerData: CustomerData = {
  basic_info: {
    company_name: '',
    business_abn: '',
    website_url: '',
    user_type: 'tradie',
    status: 'active'
  },
  contact_info: {
    primary_email: '',
    secondary_emails: [],
    phone: '',
    mobile: '',
    address: {
      street: '',
      suburb: '',
      state: '',
      postcode: ''
    }
  },
  tradie_details: {
    tradie_type: '',
    specializations: [],
    service_areas: {
      suburbs: [],
      postcodes: [],
      radius_km: 50
    },
    business_hours: {
      monday: { open: '09:00', close: '17:00' },
      tuesday: { open: '09:00', close: '17:00' },
      wednesday: { open: '09:00', close: '17:00' },
      thursday: { open: '09:00', close: '17:00' },
      friday: { open: '09:00', close: '17:00' },
      saturday: { open: '09:00', close: '17:00' },
      sunday: { open: '09:00', close: '17:00' },
      timezone: 'Australia/Sydney'
    }
  },
  ai_settings: {
    greeting_message: 'Hello! How can I help you with your project today?',
    custom_instructions: '',
    response_tone: 'professional',
    max_quote_amount: 10000
  }
};

interface CustomerOnboardingFormProps {
  onLogout: () => void;
}

const CustomerOnboardingForm: React.FC<CustomerOnboardingFormProps> = ({ onLogout }) => {
  const [customerData, setCustomerData] = useState<CustomerData>(initialCustomerData);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const { getAuthToken } = useAuth();

  const handleInputChange = (section: keyof CustomerData, field: string, value: any) => {
    setCustomerData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
    
    // Clear validation error when user starts typing
    const errorKey = `${section}.${field}`;
    if (validationErrors[errorKey]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[errorKey];
        return newErrors;
      });
    }
  };

  const handleNestedInputChange = (section: keyof CustomerData, nestedSection: string, field: string, value: any) => {
    setCustomerData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [nestedSection]: {
          ...(prev[section] as any)[nestedSection],
          [field]: value
        }
      }
    }));
  };





  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};
    
    // Basic info validation
    if (!customerData.basic_info.company_name) {
      errors['basic_info.company_name'] = 'Company name is required';
    }
    if (!customerData.basic_info.business_abn) {
      errors['basic_info.business_abn'] = 'ABN is required';
    }
    if (!customerData.basic_info.website_url) {
      errors['basic_info.website_url'] = 'Website URL is required';
    }
    
    // Contact info validation
    if (!customerData.contact_info.primary_email) {
      errors['contact_info.primary_email'] = 'Primary email is required';
    }
    if (!customerData.contact_info.address.street) {
      errors['contact_info.address.street'] = 'Street address is required';
    }
    if (!customerData.contact_info.address.suburb) {
      errors['contact_info.address.suburb'] = 'Suburb is required';
    }
    if (!customerData.contact_info.address.state) {
      errors['contact_info.address.state'] = 'State is required';
    }
    if (!customerData.contact_info.address.postcode) {
      errors['contact_info.address.postcode'] = 'Postcode is required';
    }
    
    // Tradie details validation
    if (!customerData.tradie_details.tradie_type) {
      errors['tradie_details.tradie_type'] = 'Tradie type is required';
    }
    if (customerData.tradie_details.specializations.length === 0) {
      errors['tradie_details.specializations'] = 'At least one specialization is required';
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      setError('Please fix the validation errors before submitting');
      return;
    }

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const token = await getAuthToken();
      if (!token) {
        throw new Error('Authentication token not available');
      }

      const response = await fetch('http://127.0.0.1:8080/api/onboard', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(customerData)
      });

      const result = await response.json();

      if (!response.ok) {
        if (result.details) {
          setValidationErrors(result.details);
        }
        throw new Error(result.error || 'Failed to onboard customer');
      }

      setSuccess(`Customer onboarded successfully! Client ID: ${result.client_id}`);
      setCustomerData(initialCustomerData);
      setValidationErrors({});
      
    } catch (error: any) {
      console.error('Onboarding error:', error);
      setError(error.message || 'Failed to onboard customer');
    } finally {
      setLoading(false);
    }
  };

  const renderError = (field: string) => {
    if (validationErrors[field]) {
      return <div className="field-error">{validationErrors[field]}</div>;
    }
    return null;
  };

  return (
    <div className="onboarding-container">
      <div className="onboarding-header">
        <h1>Customer Onboarding</h1>
        <button onClick={onLogout} className="logout-button">
          Logout
        </button>
      </div>

      <div className="onboarding-form-card">
        {error && (
          <div className="error-message" role="alert">
            {error}
          </div>
        )}

        {success && (
          <div className="success-message" role="alert">
            {success}
          </div>
        )}

        <form onSubmit={handleSubmit}>
          {/* Basic Information Section */}
          <div className="form-section">
            <h2 className="form-section-title">Basic Information</h2>
            
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="company_name">Company Name *</label>
                <input
                  type="text"
                  id="company_name"
                  value={customerData.basic_info.company_name}
                  onChange={(e) => handleInputChange('basic_info', 'company_name', e.target.value)}
                  required
                  disabled={loading}
                />
                {renderError('basic_info.company_name')}
              </div>

              <div className="form-group">
                <label htmlFor="business_abn">ABN *</label>
                <input
                  type="text"
                  id="business_abn"
                  value={customerData.basic_info.business_abn}
                  onChange={(e) => handleInputChange('basic_info', 'business_abn', e.target.value)}
                  placeholder="**************"
                  required
                  disabled={loading}
                />
                {renderError('basic_info.business_abn')}
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="website_url">Website URL *</label>
                <input
                  type="url"
                  id="website_url"
                  value={customerData.basic_info.website_url}
                  onChange={(e) => handleInputChange('basic_info', 'website_url', e.target.value)}
                  placeholder="https://example.com"
                  required
                  disabled={loading}
                />
                {renderError('basic_info.website_url')}
              </div>

              <div className="form-group">
                <label htmlFor="user_type">User Type</label>
                <select
                  id="user_type"
                  value={customerData.basic_info.user_type}
                  onChange={(e) => handleInputChange('basic_info', 'user_type', e.target.value)}
                  disabled={loading}
                >
                  <option value="tradie">Tradie</option>
                  <option value="business">Business</option>
                </select>
              </div>
            </div>
          </div>

          {/* Contact Information Section */}
          <div className="form-section">
            <h2 className="form-section-title">Contact Information</h2>
            
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="primary_email">Primary Email *</label>
                <input
                  type="email"
                  id="primary_email"
                  value={customerData.contact_info.primary_email}
                  onChange={(e) => handleInputChange('contact_info', 'primary_email', e.target.value)}
                  required
                  disabled={loading}
                />
                {renderError('contact_info.primary_email')}
              </div>

              <div className="form-group">
                <label htmlFor="secondary_emails">Secondary Emails</label>
                <TagInput
                  value={customerData.contact_info.secondary_emails}
                  onChange={(tags) => handleInputChange('contact_info', 'secondary_emails', tags)}
                  placeholder="Type email and press Enter to add..."
                  disabled={loading}
                  maxTags={5}
                  suggestions={[]}
                />
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="phone">Phone</label>
                <input
                  type="tel"
                  id="phone"
                  value={customerData.contact_info.phone}
                  onChange={(e) => handleInputChange('contact_info', 'phone', e.target.value)}
                  placeholder="(02) 1234 5678"
                  disabled={loading}
                />
              </div>

              <div className="form-group">
                <label htmlFor="mobile">Mobile</label>
                <input
                  type="tel"
                  id="mobile"
                  value={customerData.contact_info.mobile}
                  onChange={(e) => handleInputChange('contact_info', 'mobile', e.target.value)}
                  placeholder="0412 345 678"
                  disabled={loading}
                />
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="street">Street Address *</label>
                <input
                  type="text"
                  id="street"
                  value={customerData.contact_info.address.street}
                  onChange={(e) => handleNestedInputChange('contact_info', 'address', 'street', e.target.value)}
                  required
                  disabled={loading}
                />
                {renderError('contact_info.address.street')}
              </div>

              <div className="form-group">
                <label htmlFor="suburb">Suburb *</label>
                <input
                  type="text"
                  id="suburb"
                  value={customerData.contact_info.address.suburb}
                  onChange={(e) => handleNestedInputChange('contact_info', 'address', 'suburb', e.target.value)}
                  required
                  disabled={loading}
                />
                {renderError('contact_info.address.suburb')}
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="state">State *</label>
                <select
                  id="state"
                  value={customerData.contact_info.address.state}
                  onChange={(e) => handleNestedInputChange('contact_info', 'address', 'state', e.target.value)}
                  required
                  disabled={loading}
                >
                  <option value="">Select State</option>
                  <option value="NSW">NSW</option>
                  <option value="VIC">VIC</option>
                  <option value="QLD">QLD</option>
                  <option value="WA">WA</option>
                  <option value="SA">SA</option>
                  <option value="TAS">TAS</option>
                  <option value="ACT">ACT</option>
                  <option value="NT">NT</option>
                </select>
                {renderError('contact_info.address.state')}
              </div>

              <div className="form-group">
                <label htmlFor="postcode">Postcode *</label>
                <input
                  type="text"
                  id="postcode"
                  value={customerData.contact_info.address.postcode}
                  onChange={(e) => handleNestedInputChange('contact_info', 'address', 'postcode', e.target.value)}
                  pattern="[0-9]{4}"
                  placeholder="2000"
                  required
                  disabled={loading}
                />
                {renderError('contact_info.address.postcode')}
              </div>
            </div>
          </div>

          {/* Tradie Details Section */}
          <div className="form-section">
            <h2 className="form-section-title">Tradie Details</h2>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="tradie_type">Tradie Type *</label>
                <select
                  id="tradie_type"
                  value={customerData.tradie_details.tradie_type}
                  onChange={(e) => handleInputChange('tradie_details', 'tradie_type', e.target.value)}
                  required
                  disabled={loading}
                >
                  <option value="">Select Tradie Type</option>
                  <option value="plumber">Plumber</option>
                  <option value="electrician">Electrician</option>
                  <option value="carpenter">Carpenter</option>
                  <option value="painter">Painter</option>
                  <option value="landscaper">Landscaper</option>
                  <option value="roofer">Roofer</option>
                  <option value="tiler">Tiler</option>
                  <option value="general">General Contractor</option>
                </select>
                {renderError('tradie_details.tradie_type')}
              </div>

              <div className="form-group">
                <label htmlFor="specializations">Specializations *</label>
                <TagInput
                  value={customerData.tradie_details.specializations}
                  onChange={(tags) => handleInputChange('tradie_details', 'specializations', tags)}
                  placeholder="Type and press Enter to add specializations..."
                  disabled={loading}
                  maxTags={8}
                />
                {renderError('tradie_details.specializations')}
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="service_suburbs">Service Suburbs (comma-separated)</label>
                <input
                  type="text"
                  id="service_suburbs"
                  value={customerData.tradie_details.service_areas.suburbs.join(', ')}
                  onChange={(e) => handleNestedInputChange('tradie_details', 'service_areas', 'suburbs', e.target.value.split(',').map(s => s.trim()).filter(s => s))}
                  placeholder="Sydney, Parramatta, Liverpool"
                  disabled={loading}
                />
              </div>

              <div className="form-group">
                <label htmlFor="service_postcodes">Service Postcodes (comma-separated)</label>
                <input
                  type="text"
                  id="service_postcodes"
                  value={customerData.tradie_details.service_areas.postcodes.join(', ')}
                  onChange={(e) => handleNestedInputChange('tradie_details', 'service_areas', 'postcodes', e.target.value.split(',').map(s => s.trim()).filter(s => s))}
                  placeholder="2000, 2150, 2170"
                  disabled={loading}
                />
              </div>
            </div>

            <div className="form-row single">
              <div className="form-group">
                <label htmlFor="radius_km">Service Radius (km)</label>
                <input
                  type="number"
                  id="radius_km"
                  value={customerData.tradie_details.service_areas.radius_km}
                  onChange={(e) => handleNestedInputChange('tradie_details', 'service_areas', 'radius_km', parseInt(e.target.value) || 0)}
                  min="1"
                  max="200"
                  disabled={loading}
                />
              </div>
            </div>
          </div>

          {/* AI Settings Section */}
          <div className="form-section">
            <h2 className="form-section-title">AI Settings</h2>

            <div className="form-row">
              <div className="form-group full-width">
                <label htmlFor="greeting_message">Greeting Message</label>
                <textarea
                  id="greeting_message"
                  value={customerData.ai_settings.greeting_message}
                  onChange={(e) => handleInputChange('ai_settings', 'greeting_message', e.target.value)}
                  placeholder="Hello! How can I help you with your project today?"
                  disabled={loading}
                />
              </div>
            </div>

            <div className="form-row">
              <div className="form-group full-width">
                <label htmlFor="custom_instructions">Custom Instructions</label>
                <textarea
                  id="custom_instructions"
                  value={customerData.ai_settings.custom_instructions}
                  onChange={(e) => handleInputChange('ai_settings', 'custom_instructions', e.target.value)}
                  placeholder="Additional instructions for the AI assistant..."
                  disabled={loading}
                />
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="response_tone">Response Tone</label>
                <select
                  id="response_tone"
                  value={customerData.ai_settings.response_tone}
                  onChange={(e) => handleInputChange('ai_settings', 'response_tone', e.target.value)}
                  disabled={loading}
                >
                  <option value="professional">Professional</option>
                  <option value="friendly">Friendly</option>
                  <option value="casual">Casual</option>
                  <option value="formal">Formal</option>
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="max_quote_amount">Max Quote Amount ($)</label>
                <input
                  type="number"
                  id="max_quote_amount"
                  value={customerData.ai_settings.max_quote_amount}
                  onChange={(e) => handleInputChange('ai_settings', 'max_quote_amount', parseInt(e.target.value) || 0)}
                  min="100"
                  max="100000"
                  step="100"
                  disabled={loading}
                />
              </div>
            </div>
          </div>

          <button
            type="submit"
            className="submit-button"
            disabled={loading}
          >
            {loading ? (
              <span className="loading-content">
                <span className="loading-spinner"></span>
                Onboarding Customer...
              </span>
            ) : (
              'Onboard Customer'
            )}
          </button>
        </form>
      </div>
    </div>
  );
};

export default CustomerOnboardingForm;
