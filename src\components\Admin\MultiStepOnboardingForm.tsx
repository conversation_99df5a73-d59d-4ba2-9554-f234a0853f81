/**
 * Multi-Step Customer Onboarding Form
 * A modern, wizard-style onboarding experience with step navigation and animations
 */

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useForm, FormProvider } from 'react-hook-form';
import StepWizard from 'react-step-wizard';
import { useAuth } from '../../contexts/AuthContext';

// Step Components
import BasicInfoStep from './steps/BasicInfoStep';
import ContactInfoStep from './steps/ContactInfoStep';
import BusinessDetailsStep from './steps/BusinessDetailsStep';
import AISettingsStep from './steps/AISettingsStep';
import StepNavigation from './StepNavigation';

interface CustomerData {
  basic_info: {
    company_name: string;
    business_abn: string;
    website_url: string;
    user_type: string;
    status: string;
  };
  contact_info: {
    primary_email: string;
    secondary_emails: string[];
    phone: string;
    mobile: string;
    address: {
      street: string;
      suburb: string;
      state: string;
      postcode: string;
    };
  };
  tradie_details: {
    tradie_type: string;
    specializations: string[];
    service_areas: {
      suburbs: string[];
      postcodes: string[];
      radius_km: number;
    };
    business_hours: {
      monday: { open: string; close: string };
      tuesday: { open: string; close: string };
      wednesday: { open: string; close: string };
      thursday: { open: string; close: string };
      friday: { open: string; close: string };
      saturday: { open: string; close: string };
      sunday: { open: string; close: string };
      timezone: string;
    };
  };
  ai_settings: {
    greeting_message: string;
    custom_instructions: string;
    response_tone: string;
    max_quote_amount: number;
  };
}

const initialCustomerData: CustomerData = {
  basic_info: {
    company_name: '',
    business_abn: '',
    website_url: '',
    user_type: 'tradie',
    status: 'active'
  },
  contact_info: {
    primary_email: '',
    secondary_emails: [],
    phone: '',
    mobile: '',
    address: {
      street: '',
      suburb: '',
      state: '',
      postcode: ''
    }
  },
  tradie_details: {
    tradie_type: '',
    specializations: [],
    service_areas: {
      suburbs: [],
      postcodes: [],
      radius_km: 50
    },
    business_hours: {
      monday: { open: '09:00', close: '17:00' },
      tuesday: { open: '09:00', close: '17:00' },
      wednesday: { open: '09:00', close: '17:00' },
      thursday: { open: '09:00', close: '17:00' },
      friday: { open: '09:00', close: '17:00' },
      saturday: { open: '09:00', close: '17:00' },
      sunday: { open: '09:00', close: '17:00' },
      timezone: 'Australia/Sydney'
    }
  },
  ai_settings: {
    greeting_message: 'Hello! How can I help you with your project today?',
    custom_instructions: '',
    response_tone: 'professional',
    max_quote_amount: 10000
  }
};

interface MultiStepOnboardingFormProps {
  onLogout: () => void;
}

const MultiStepOnboardingForm: React.FC<MultiStepOnboardingFormProps> = ({ onLogout }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const { getAuthToken } = useAuth();
  const [wizardInstance, setWizardInstance] = useState<any>(null);

  const methods = useForm<CustomerData>({
    defaultValues: initialCustomerData,
    mode: 'onChange'
  });

  const steps = [
    { id: 1, title: 'Company', icon: '🏢' },
    { id: 2, title: 'Contact', icon: '📞' },
    { id: 3, title: 'Business', icon: '🔧' },
    { id: 4, title: 'AI Setup', icon: '🤖' }
  ];

  const handleStepChange = (stepInfo: any) => {
    // If moving forward, mark the previous step as completed
    if (stepInfo.activeStep > currentStep) {
      if (!completedSteps.includes(currentStep)) {
        setCompletedSteps([...completedSteps, currentStep]);
      }
    }
    setCurrentStep(stepInfo.activeStep);
  };



  const handleStepClick = (stepId: number) => {
    if (completedSteps.includes(stepId) || stepId === currentStep) {
      if (wizardInstance) {
        wizardInstance.goToStep(stepId);
      }
    }
  };

  const handleSubmit = async (data: CustomerData) => {
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const token = await getAuthToken();
      if (!token) {
        throw new Error('Authentication token not available');
      }

      const response = await fetch('http://127.0.0.1:8080/api/onboard', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(data)
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to onboard customer');
      }

      setSuccess(`Customer onboarded successfully! Client ID: ${result.client_id}`);

      // Reset form and wizard state
      setTimeout(() => {
        methods.reset(initialCustomerData);
        setCompletedSteps([]);
        setCurrentStep(1);

        // Reset wizard instance to step 1
        if (wizardInstance) {
          wizardInstance.goToStep(1);
        }

        // Clear success message after showing it briefly
        setTimeout(() => {
          setSuccess('');
        }, 5000);
      }, 1000); // Brief delay to show success message
    } catch (error: any) {
      console.error('Onboarding error:', error);
      setError(error.message || 'Failed to onboard customer');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="multi-step-onboarding-container">
      <div className="onboarding-header">
        <h1>Customer Onboarding</h1>
        <button onClick={onLogout} className="logout-button">
          Logout
        </button>
      </div>

      <div className="wizard-container">
        <StepNavigation
          steps={steps}
          currentStep={currentStep}
          completedSteps={completedSteps}
          onStepClick={handleStepClick}
        />

        <FormProvider {...methods}>
          <StepWizard
            instance={setWizardInstance}
            onStepChange={handleStepChange}
          >
            <BasicInfoStep
              loading={loading}
            />
            <ContactInfoStep
              loading={loading}
            />
            <BusinessDetailsStep
              loading={loading}
            />
            <AISettingsStep
              onSubmit={methods.handleSubmit(handleSubmit)}
              loading={loading}
            />
          </StepWizard>
        </FormProvider>

        {error && (
          <motion.div 
            className="error-message"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            role="alert"
          >
            {error}
          </motion.div>
        )}

        {success && (
          <motion.div
            className="success-message"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            role="alert"
          >
            <div>
              {success}
            </div>
            <div style={{ fontSize: '0.875rem', marginTop: '0.5rem', opacity: 0.8 }}>
              Form will be reset for the next customer entry...
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default MultiStepOnboardingForm;
