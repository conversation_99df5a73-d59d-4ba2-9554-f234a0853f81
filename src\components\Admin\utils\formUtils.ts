/**
 * Form utility functions for handling errors and validation
 */

export const getErrorMessage = (errors: any, path: string): string | null => {
  const keys = path.split('.');
  let current: any = errors;
  for (const key of keys) {
    if (current?.[key]) {
      current = current[key];
    } else {
      return null;
    }
  }
  return current?.message || null;
};

export const hasError = (errors: any, path: string): boolean => {
  return getErrorMessage(errors, path) !== null;
};
