#!/usr/bin/env python3
"""
Test script for the customer onboarding API endpoint.
This tests the validation and API functionality without requiring a frontend.
"""

import requests
import json

# Test data that should pass validation
valid_customer_data = {
    "basic_info": {
        "company_name": "Test Plumbing Services",
        "business_abn": "***********",
        "website_url": "https://testplumbing.com.au",
        "user_type": "tradie",
        "status": "active"
    },
    "contact_info": {
        "primary_email": "<EMAIL>",
        "secondary_emails": ["<EMAIL>"],
        "phone": "0212345678",
        "mobile": "0412345678",
        "address": {
            "street": "123 Test Street",
            "suburb": "Sydney",
            "state": "NSW",
            "postcode": "2000"
        }
    },
    "tradie_details": {
        "tradie_type": "plumber",
        "specializations": ["Bathroom renovation", "Kitchen plumbing", "Emergency repairs"],
        "service_areas": {
            "suburbs": ["Sydney", "Parramatta", "Liverpool"],
            "postcodes": ["2000", "2150", "2170"],
            "radius_km": 50
        },
        "business_hours": {
            "monday": {"open": "08:00", "close": "17:00"},
            "tuesday": {"open": "08:00", "close": "17:00"},
            "wednesday": {"open": "08:00", "close": "17:00"},
            "thursday": {"open": "08:00", "close": "17:00"},
            "friday": {"open": "08:00", "close": "17:00"},
            "saturday": {"open": "09:00", "close": "15:00"},
            "sunday": {"open": "09:00", "close": "15:00"},
            "timezone": "Australia/Sydney"
        }
    },
    "ai_settings": {
        "greeting_message": "G'day! How can I help you with your plumbing needs today?",
        "custom_instructions": "Always ask for location and urgency of the job. Provide rough estimates when possible.",
        "response_tone": "friendly",
        "max_quote_amount": 5000
    }
}

# Test data that should fail validation
invalid_customer_data = {
    "basic_info": {
        "company_name": "",  # Missing required field
        "business_abn": "invalid-abn",  # Invalid ABN format
        "website_url": "not-a-url",  # Invalid URL
        "user_type": "tradie",
        "status": "active"
    },
    "contact_info": {
        "primary_email": "invalid-email",  # Invalid email
        "address": {
            "street": "",  # Missing required field
            "suburb": "Sydney",
            "state": "NSW",
            "postcode": "invalid"  # Invalid postcode
        }
    }
}

def test_onboarding_endpoint():
    """Test the onboarding API endpoint."""
    
    base_url = "http://localhost:8080"
    endpoint = f"{base_url}/api/onboard"
    
    print("Testing Customer Onboarding API")
    print("=" * 40)
    
    # Test 1: Valid data without authentication (should fail with 401)
    print("\n1. Testing without authentication (should fail with 401)...")
    try:
        response = requests.post(endpoint, json=valid_customer_data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        
        if response.status_code == 401:
            print("✅ Correctly rejected unauthenticated request")
        else:
            print("❌ Should have rejected unauthenticated request")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 2: Invalid data format (should fail with 400)
    print("\n2. Testing with invalid data (should fail with 400)...")
    try:
        # Note: This will also fail with 401 due to no auth, but we're testing the concept
        response = requests.post(endpoint, json=invalid_customer_data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 3: Server health check
    print("\n3. Testing server health...")
    try:
        health_response = requests.get(f"{base_url}/v1/health")
        print(f"Health Status Code: {health_response.status_code}")
        print(f"Health Response: {health_response.json()}")
        
        if health_response.status_code == 200:
            print("✅ Server is healthy")
        else:
            print("❌ Server health check failed")
    except Exception as e:
        print(f"❌ Health check error: {e}")
    
    print("\n" + "=" * 40)
    print("Test Summary:")
    print("- The API endpoint is protected by authentication (good!)")
    print("- To test with valid authentication, you need to:")
    print("  1. Create an admin user using test_admin_user.py")
    print("  2. Get a Firebase ID token from the frontend")
    print("  3. Include it in the Authorization header")
    print("\nNext steps:")
    print("- Run the Flask server: python main.py")
    print("- Create an admin user: python test_admin_user.py")
    print("- Open the admin interface: http://localhost:5173/admin.html")

if __name__ == "__main__":
    test_onboarding_endpoint()
